"""
Android端服务
"""

import os
import subprocess
import asyncio
import requests
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from pathlib import Path

from app.models.device import AndroidDevice, AppPackage
from app.core.config import settings

class AndroidService:
    """Android端服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.current_device = None
        
    async def get_devices(self) -> List[Dict[str, Any]]:
        """获取Android设备列表"""
        try:
            # 执行adb devices命令
            result = subprocess.run(
                ["adb", "devices"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            devices = []
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # 跳过第一行标题
                for line in lines:
                    if line.strip() and '\t' in line:
                        device_id, status = line.split('\t')
                        if status == 'device':
                            device_info = await self._get_device_info(device_id)
                            devices.append(device_info)
            
            # 更新数据库中的设备信息
            await self._update_devices_in_db(devices)
            
            return devices
            
        except Exception as e:
            print(f"获取设备列表失败: {e}")
            return []
    
    async def _get_device_info(self, device_id: str) -> Dict[str, Any]:
        """获取设备详细信息"""
        try:
            # 获取设备属性
            props = {}
            prop_commands = {
                'brand': 'ro.product.brand',
                'model': 'ro.product.model',
                'android_version': 'ro.build.version.release'
            }
            
            for key, prop in prop_commands.items():
                result = subprocess.run(
                    ["adb", "-s", device_id, "shell", "getprop", prop],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                if result.returncode == 0:
                    props[key] = result.stdout.strip()
            
            # 获取屏幕分辨率
            result = subprocess.run(
                ["adb", "-s", device_id, "shell", "wm", "size"],
                capture_output=True,
                text=True,
                timeout=5
            )
            screen_resolution = "unknown"
            if result.returncode == 0:
                output = result.stdout.strip()
                if "Physical size:" in output:
                    screen_resolution = output.split("Physical size: ")[1]
            
            return {
                "device_id": device_id,
                "device_name": f"{props.get('brand', 'Unknown')} {props.get('model', 'Device')}",
                "brand": props.get('brand'),
                "model": props.get('model'),
                "android_version": props.get('android_version'),
                "screen_resolution": screen_resolution,
                "is_connected": True,
                "is_active": False
            }
            
        except Exception as e:
            print(f"获取设备信息失败: {e}")
            return {
                "device_id": device_id,
                "device_name": f"Device {device_id}",
                "is_connected": True,
                "is_active": False
            }
    
    async def _update_devices_in_db(self, devices: List[Dict[str, Any]]):
        """更新数据库中的设备信息"""
        try:
            # 获取当前数据库中的设备
            db_devices = self.db.query(AndroidDevice).all()
            db_device_ids = {device.device_id for device in db_devices}
            
            # 更新或创建设备记录
            for device_info in devices:
                device_id = device_info["device_id"]
                
                if device_id in db_device_ids:
                    # 更新现有设备
                    device = self.db.query(AndroidDevice).filter(
                        AndroidDevice.device_id == device_id
                    ).first()
                    if device:
                        device.device_name = device_info.get("device_name")
                        device.brand = device_info.get("brand")
                        device.model = device_info.get("model")
                        device.android_version = device_info.get("android_version")
                        device.screen_resolution = device_info.get("screen_resolution")
                        device.is_connected = True
                else:
                    # 创建新设备
                    device = AndroidDevice(
                        device_id=device_id,
                        device_name=device_info.get("device_name"),
                        brand=device_info.get("brand"),
                        model=device_info.get("model"),
                        android_version=device_info.get("android_version"),
                        screen_resolution=device_info.get("screen_resolution"),
                        is_connected=True
                    )
                    self.db.add(device)
            
            # 标记未连接的设备
            current_device_ids = {device["device_id"] for device in devices}
            for device in db_devices:
                if device.device_id not in current_device_ids:
                    device.is_connected = False
            
            self.db.commit()
            
        except Exception as e:
            print(f"更新设备数据库失败: {e}")
            self.db.rollback()
    
    async def select_device(self, device_id: str) -> Dict[str, Any]:
        """选择当前活动设备"""
        try:
            # 取消所有设备的活动状态
            self.db.query(AndroidDevice).update({"is_active": False})
            
            # 设置指定设备为活动状态
            device = self.db.query(AndroidDevice).filter(
                AndroidDevice.device_id == device_id
            ).first()
            
            if not device:
                return {"success": False, "message": "设备不存在"}
            
            if not device.is_connected:
                return {"success": False, "message": "设备未连接"}
            
            device.is_active = True
            self.db.commit()
            
            self.current_device = device
            
            return {
                "success": True,
                "message": f"已选择设备: {device.device_name}",
                "device": {
                    "device_id": device.device_id,
                    "device_name": device.device_name,
                    "brand": device.brand,
                    "model": device.model
                }
            }
            
        except Exception as e:
            self.db.rollback()
            return {"success": False, "message": f"选择设备失败: {str(e)}"}
    
    async def refresh_devices(self) -> List[Dict[str, Any]]:
        """刷新设备列表"""
        return await self.get_devices()
    
    async def install_apk(self, download_url: str, device_id: Optional[str] = None) -> Dict[str, Any]:
        """下载并安装APK"""
        try:
            # 如果没有指定设备，使用当前活动设备
            if not device_id:
                active_device = self.db.query(AndroidDevice).filter(
                    AndroidDevice.is_active == True
                ).first()
                if not active_device:
                    return {"success": False, "message": "没有活动设备"}
                device_id = active_device.device_id
            
            # 下载APK文件
            download_dir = settings.OUTPUT_DIR / "downloads"
            download_dir.mkdir(exist_ok=True)
            
            filename = download_url.split('/')[-1]
            if not filename.endswith('.apk'):
                filename += '.apk'
            
            file_path = download_dir / filename
            
            # 下载文件
            response = requests.get(download_url, stream=True)
            response.raise_for_status()
            
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            # 安装APK
            result = subprocess.run(
                ["adb", "-s", device_id, "install", str(file_path)],
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                # 记录到数据库
                app_package = AppPackage(
                    download_url=download_url,
                    file_path=str(file_path),
                    file_size=file_path.stat().st_size,
                    is_downloaded=True,
                    is_installed=True,
                    device_id=device_id
                )
                self.db.add(app_package)
                self.db.commit()
                
                return {
                    "success": True,
                    "message": "APK安装成功",
                    "file_path": str(file_path)
                }
            else:
                return {
                    "success": False,
                    "message": f"APK安装失败: {result.stderr}"
                }
                
        except Exception as e:
            return {"success": False, "message": f"安装APK失败: {str(e)}"}
    
    async def execute_adb_command(self, command: str, device_id: Optional[str] = None) -> Dict[str, Any]:
        """执行ADB命令"""
        try:
            # 如果没有指定设备，使用当前活动设备
            if not device_id:
                active_device = self.db.query(AndroidDevice).filter(
                    AndroidDevice.is_active == True
                ).first()
                if not active_device:
                    return {"success": False, "message": "没有活动设备"}
                device_id = active_device.device_id
            
            # 构建完整的ADB命令
            full_command = ["adb", "-s", device_id] + command.split()
            
            # 执行命令
            result = subprocess.run(
                full_command,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            return {
                "success": result.returncode == 0,
                "command": " ".join(full_command),
                "stdout": result.stdout,
                "stderr": result.stderr,
                "return_code": result.returncode
            }
            
        except Exception as e:
            return {"success": False, "message": f"执行ADB命令失败: {str(e)}"}
    
    async def take_screenshot(self, device_id: str) -> Dict[str, Any]:
        """获取设备截图"""
        try:
            # 生成截图文件名
            timestamp = int(asyncio.get_event_loop().time())
            filename = f"android_screenshot_{timestamp}.png"
            file_path = settings.SCREENSHOTS_DIR / filename
            
            # 执行截图命令
            result = subprocess.run(
                ["adb", "-s", device_id, "exec-out", "screencap", "-p"],
                capture_output=True,
                timeout=10
            )
            
            if result.returncode == 0:
                with open(file_path, 'wb') as f:
                    f.write(result.stdout)
                
                return {
                    "success": True,
                    "file_path": str(file_path),
                    "url": f"/output/screenshots/{filename}"
                }
            else:
                return {"success": False, "message": "截图失败"}
                
        except Exception as e:
            return {"success": False, "message": f"获取截图失败: {str(e)}"}
    
    async def get_device_status(self, device_id: str) -> Dict[str, Any]:
        """获取设备状态"""
        try:
            device = self.db.query(AndroidDevice).filter(
                AndroidDevice.device_id == device_id
            ).first()
            
            if not device:
                return {"success": False, "message": "设备不存在"}
            
            return {
                "success": True,
                "device": {
                    "device_id": device.device_id,
                    "device_name": device.device_name,
                    "is_connected": device.is_connected,
                    "is_active": device.is_active,
                    "brand": device.brand,
                    "model": device.model,
                    "android_version": device.android_version,
                    "screen_resolution": device.screen_resolution
                }
            }
            
        except Exception as e:
            return {"success": False, "message": f"获取设备状态失败: {str(e)}"}

"""
AI执行服务
"""

import json
import asyncio
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session
from pathlib import Path

from app.core.config import settings

class AIService:
    """AI执行服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.config = {
            "model_name": settings.DEFAULT_AI_MODEL,
            "api_base": settings.DEFAULT_API_BASE,
            "temperature": settings.DEFAULT_TEMPERATURE
        }
    
    async def process_complex_ai_exec_web(
        self,
        desc: str,
        last_result: str = "",
        last_step_result: str = "",
        max_scroll_times: int = 3
    ) -> Dict[str, Any]:
        """Web端AI执行"""
        try:
            print(f"Web端AI执行: {desc}")
            
            # 这里应该实现完整的AI执行逻辑
            # 包括获取DOM树、截图、调用大模型等
            
            # 模拟AI执行结果
            result = {
                "content": desc,
                "step_list": [
                    {
                        "step_index": 1,
                        "action": "点击搜索框"
                    },
                    {
                        "step_index": 2,
                        "action": "输入搜索内容"
                    },
                    {
                        "step_index": 3,
                        "action": "点击搜索按钮"
                    }
                ],
                "next_executed_step": {
                    "step_index": 1,
                    "code_info": {
                        "type": "Action",
                        "type_thought": "需要点击搜索框进行输入",
                        "code_thought": "通过seq_index定位搜索框并点击",
                        "code_generate": "self.clickByCoordinate(10)"
                    },
                    "element_info": {
                        "find_status": 1,
                        "thought": "在页面中找到了搜索框元素",
                        "seq_index": 10,
                        "seq_index_list": [10, 11, 12]
                    },
                    "observations": "成功点击搜索框，光标已聚焦"
                },
                "test_progress": {
                    "completed_steps": [],
                    "remaining_steps": ["点击搜索框", "输入搜索内容", "点击搜索按钮"]
                },
                "result": -1  # 首次执行
            }
            
            return json.dumps(result, ensure_ascii=False)
            
        except Exception as e:
            return json.dumps({
                "error": f"Web端AI执行失败: {str(e)}"
            }, ensure_ascii=False)
    
    async def process_complex_ai_exec_android(
        self,
        desc: str,
        last_result: str = "",
        last_step_result: str = "",
        max_scroll_times: int = 3
    ) -> Dict[str, Any]:
        """Android端AI执行"""
        try:
            print(f"Android端AI执行: {desc}")
            
            # 这里应该实现完整的AI执行逻辑
            # 包括获取DOM树、截图、调用大模型等
            
            # 模拟AI执行结果
            result = {
                "content": desc,
                "step_list": [
                    {
                        "step_index": 1,
                        "action": "点击应用图标"
                    },
                    {
                        "step_index": 2,
                        "action": "等待应用启动"
                    },
                    {
                        "step_index": 3,
                        "action": "执行指定操作"
                    }
                ],
                "next_executed_step": {
                    "step_index": 1,
                    "code_info": {
                        "type": "Action",
                        "type_thought": "需要点击应用图标启动应用",
                        "code_thought": "通过seq_index定位应用图标并点击",
                        "code_generate": "self.clickByCoordinate(5)"
                    },
                    "element_info": {
                        "find_status": 1,
                        "thought": "在屏幕中找到了应用图标",
                        "seq_index": 5,
                        "seq_index_list": [5, 6, 7]
                    },
                    "observations": "成功点击应用图标，应用正在启动"
                },
                "test_progress": {
                    "completed_steps": [],
                    "remaining_steps": ["点击应用图标", "等待应用启动", "执行指定操作"]
                },
                "result": -1  # 首次执行
            }
            
            return json.dumps(result, ensure_ascii=False)
            
        except Exception as e:
            return json.dumps({
                "error": f"Android端AI执行失败: {str(e)}"
            }, ensure_ascii=False)
    
    async def set_config(
        self,
        model_name: str,
        api_base: str,
        temperature: float = 0.7
    ) -> Dict[str, Any]:
        """设置AI配置"""
        try:
            self.config.update({
                "model_name": model_name,
                "api_base": api_base,
                "temperature": temperature
            })
            
            return {
                "success": True,
                "message": "AI配置设置成功",
                "config": self.config
            }
            
        except Exception as e:
            return {"success": False, "message": f"设置AI配置失败: {str(e)}"}
    
    async def get_config(self) -> Dict[str, Any]:
        """获取AI配置"""
        return {
            "success": True,
            "config": self.config
        }
    
    async def test_connection(
        self,
        model_name: str,
        api_base: str
    ) -> Dict[str, Any]:
        """测试AI连接"""
        try:
            # 这里应该实现真实的连接测试
            # 暂时模拟测试结果
            
            await asyncio.sleep(1)  # 模拟网络请求
            
            return {
                "success": True,
                "message": "AI连接测试成功",
                "model_name": model_name,
                "api_base": api_base,
                "response_time": "1.2s"
            }
            
        except Exception as e:
            return {"success": False, "message": f"AI连接测试失败: {str(e)}"}
    
    def _load_prompt_template(self, automation_type: str) -> str:
        """加载提示词模板"""
        try:
            prompt_file = settings.PROJECT_ROOT / "prompt" / f"complex_ai_exec_{automation_type}.md"
            if prompt_file.exists():
                with open(prompt_file, 'r', encoding='utf-8') as f:
                    return f.read()
            else:
                return "默认提示词模板"
        except Exception as e:
            print(f"加载提示词模板失败: {e}")
            return "默认提示词模板"
    
    def _format_prompt(
        self,
        template: str,
        desc: str,
        viewport: str,
        is_scrollable: bool,
        max_scroll_times: int,
        last_result: str,
        last_step_result: str
    ) -> str:
        """格式化提示词"""
        try:
            # 替换模板中的占位符
            formatted_prompt = template.replace("{{desc}}", desc)
            formatted_prompt = formatted_prompt.replace("{{viewport}}", viewport)
            formatted_prompt = formatted_prompt.replace("{{is_scrollable}}", str(is_scrollable))
            formatted_prompt = formatted_prompt.replace("{{max_scroll_times}}", str(max_scroll_times))
            formatted_prompt = formatted_prompt.replace("{{last_result}}", last_result)
            formatted_prompt = formatted_prompt.replace("{{last_step_result}}", last_step_result)
            
            return formatted_prompt
            
        except Exception as e:
            print(f"格式化提示词失败: {e}")
            return template
